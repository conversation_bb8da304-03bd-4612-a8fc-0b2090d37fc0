<?php
// generateAvailabilityReport.php - PHP-based availability report generator for hostApm.php
// Replaces the JavaScript PDF generation with server-side PHP generation using FPDF

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// Load environment variables (DB/Nagios credentials)
require_once dirname(__DIR__, 2) . '/loadenv.php';

// ------------------------- Helper functions (shared with saveReport.php) -------------------------------
/**
 * Get DB connection to 'blesk' for retrieving user credentials
 */
function getDatabaseConnectionAdminUser(): mysqli {
    $conn = new mysqli($_ENV['DB_SERVER'], $_ENV['DB_USER'], $_ENV['DB_PASSWORD'], 'blesk');
    if ($conn->connect_error) {
        die('DB connection failed: ' . $conn->connect_error);
    }
    return $conn;
}

/**
 * Fetch Nagios HTTP basic auth credentials (user_id=1)
 */
function getUserCredentials(): ?array {
    $conn = getDatabaseConnectionAdminUser();
    $result = $conn->query("SELECT username, password FROM users WHERE user_id = 1 LIMIT 1");
    $cred = null;
    if ($result && $row = $result->fetch_assoc()) {
        $cred = ['user' => $row['username'], 'pass' => $row['password']];
    }
    $conn->close();
    return $cred;
}

/**
 * Determine self IP address (stored by bubblemaps)
 */
function getSelfIp(): string {
    $ip = trim(@file_get_contents('/etc/sysconfig/ipaddr'));
    if (!$ip) die("Unable to determine self IP");
    return $ip;
}

// -----------------------------------------------------------------------------
// Configuration / constants
// -----------------------------------------------------------------------------
$FPDF_PATH     = dirname(__DIR__) . '/reportsFunctions/fpdf.php'; // FPDF library path
$NAGIOS_BASE   = 'https://' . getSelfIp(); // Use real host/IP instead of localhost

// -----------------------------------------------------------------------------
// Get POST parameters
// -----------------------------------------------------------------------------
$hostname = isset($_POST['hostname']) ? trim($_POST['hostname']) : '';
$hostIp = isset($_POST['hostIp']) ? trim($_POST['hostIp']) : '';
$startTs = isset($_POST['startTs']) ? intval($_POST['startTs']) : 0;
$endTs = isset($_POST['endTs']) ? intval($_POST['endTs']) : 0;

// Validate parameters
if (empty($hostname)) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Hostname is required']);
    exit;
}

if ($startTs <= 0 || $endTs <= 0 || $endTs <= $startTs) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid time range']);
    exit;
}

// -----------------------------------------------------------------------------
// Helper to fetch Nagios availability data
// -----------------------------------------------------------------------------
function fetchJson(string $path, int $startTs, int $endTs): array
{
    $url = $GLOBALS['NAGIOS_BASE'] . $path . '&starttime=' . $startTs . '&endtime=' . $endTs;

    static $creds = null;
    if ($creds === null) $creds = getUserCredentials();

    // Initialise cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_TIMEOUT, 20);
    // Allow self-signed certificates (internal Nagios)
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    // Pass basic-auth credentials if available
    if ($creds) {
        curl_setopt($ch, CURLOPT_USERPWD, $creds['user'] . ':' . $creds['pass']);
        curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
    }

    $raw = curl_exec($ch);
    if ($raw === false) {
        error_log('[generateAvailabilityReport] cURL error: ' . curl_error($ch));
        curl_close($ch);
        return [];
    }

    $http = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    if ($http !== 200) {
        error_log('[generateAvailabilityReport] HTTP ' . $http . ' fetching ' . $url);
        return [];
    }

    $json = json_decode($raw, true);
    if (!is_array($json)) return [];
    return $json['data'] ?? [];
}

// Helper to fetch state change timeline data
function fetchTimelineJson(string $hostname, int $startTs, int $endTs, string $objectType = 'host', string $serviceDescription = ''): array
{
    $path = '/nagios/cgi-bin/archivejson.cgi?query=statechangelist&formatoptions=enumerate&objecttype=' . $objectType . '&hostname=' . urlencode($hostname);
    if ($objectType === 'service' && !empty($serviceDescription)) {
        $path .= '&servicedescription=' . urlencode($serviceDescription);
    }
    $path .= '&starttime=' . $startTs . '&endtime=' . $endTs;
    
    $data = fetchJson($path, $startTs, $endTs);
    $rawList = $data['statechangelist'] ?? [];
    
    // Filter out synthetic pseudo-state entries from Nagios API
    $filteredList = array_filter($rawList, function($entry) {
        return !isset($entry['plugin_output']) || strpos($entry['plugin_output'], 'Pseudo-State') === false;
    });
    
    // Sort by timestamp
    usort($filteredList, function($a, $b) {
        return $a['timestamp'] - $b['timestamp'];
    });
    
    return $filteredList;
}

// Helper function to convert state values to labels
function stateLabel($val, $isHost) {
    if ($val === null || $val === '') return '';
    $num = intval($val);
    
    if ($isHost) {
        // Host state bitmask mapping: 1=pending,2=up,4=down,8=unreachable
        if ($num & 2) return 'UP';
        if ($num & 4) return 'DOWN';
        if ($num & 8) return 'UNREACHABLE';
        if ($num & 1) return 'PENDING';
        // Fallback for simple numeric states (0,1,2,3)
        return ['UP','DOWN','UNREACHABLE'][$num] ?? strval($num);
    } else {
        // Service state bitmask mapping: 1=pending,2=ok,4=warning,8=unknown,16=critical
        if ($num & 2) return 'OK';
        if ($num & 4) return 'WARNING';
        if ($num & 16) return 'CRITICAL';
        if ($num & 8) return 'UNKNOWN';
        if ($num & 1) return 'PENDING';
        // Fallback for simple numeric states (0,1,2,3)
        return ['OK','WARNING','CRITICAL','UNKNOWN'][$num] ?? strval($num);
    }
}

// Helper function to calculate percentage
function percent($part, $total) {
    if ($total == 0) return 0.0;
    return ($part / $total) * 100.0;
}

// -----------------------------------------------------------------------------
// Fetch data from Nagios
// -----------------------------------------------------------------------------
try {
    // Build URLs for data fetching
    $hostAvailUrl = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=hosts&hostname=' . urlencode($hostname);
    $svcAvailUrl = '/nagios/cgi-bin/archivejson.cgi?query=availability&availabilityobjecttype=services&hostname=' . urlencode($hostname);
    
    // Fetch data
    $hostData = fetchJson($hostAvailUrl, $startTs, $endTs);
    $svcData = fetchJson($svcAvailUrl, $startTs, $endTs);
    $hostTimeline = fetchTimelineJson($hostname, $startTs, $endTs, 'host');
    
    // Get services list for service timelines
    $services = $svcData['services'] ?? [];
    
} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Error fetching data: ' . $e->getMessage()]);
    exit;
}

// -----------------------------------------------------------------------------
// Generate PDF report using FPDF
// -----------------------------------------------------------------------------
$fpdfAvailable = false;
if (!class_exists('FPDF') && file_exists($FPDF_PATH)) {
    require_once $FPDF_PATH;
}
if (class_exists('FPDF')) {
    $fpdfAvailable = true;
}

if (!$fpdfAvailable) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'PDF library not available']);
    exit;
}

// After FPDF is available, define subclass for header/footer (matching saveReport.php style)
if (!class_exists('BleskAvailabilityPDF')) {
    class BleskAvailabilityPDF extends FPDF {
        public string $titleText = '';
        public string $rangeText = '';
        public string $statusText = '';
        public string $logoPath = '';

        function Header() {
            // Logo (optional)
            if ($this->logoPath && file_exists($this->logoPath)) {
                $this->Image($this->logoPath, 10, 6, 15);
            }
            // Title
            $this->SetFont('Arial', 'B', 16);
            $this->Cell(0, 8, $this->titleText, 0, 1, 'C');
            // Date range
            $this->SetFont('Arial', '', 10);
            $this->Cell(0, 6, $this->rangeText, 0, 1, 'C');
            // Status info (if available)
            if ($this->statusText) {
                $this->SetFont('Arial', '', 9);
                $this->Cell(0, 6, $this->statusText, 0, 1, 'C');
            }
            $this->Ln(5);
        }

        function Footer() {
            $this->SetY(-12);
            $this->SetFont('Arial', 'I', 8);
            $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
        }

        // Public helper to get printable width between margins
        public function ContentWidth(): float {
            return $this->GetPageWidth() - $this->lMargin - $this->rMargin;
        }

        // Helper to draw timeline bar (similar to JavaScript version)
        public function drawTimelineBar($timelineData, $startTs, $endTs, $y, $colorMap, $isHost = true) {
            $barHeight = 8;
            $barWidth = $this->ContentWidth();
            $startMs = $startTs * 1000;
            $endMs = $endTs * 1000;
            $duration = $endMs - $startMs;

            // Draw timeline background
            $this->SetFillColor(245, 245, 245);
            $this->Rect($this->lMargin, $y, $barWidth, $barHeight, 'F');
            $this->SetDrawColor(220, 220, 220);
            $this->SetLineWidth(0.5);
            $this->Rect($this->lMargin, $y, $barWidth, $barHeight, 'D');

            // Draw timeline segments
            for ($i = 0; $i < count($timelineData); $i++) {
                $cur = $timelineData[$i];
                $segStart = ($i === 0) ? $startMs : $cur['timestamp'];
                $segEnd = ($i < count($timelineData) - 1) ? $timelineData[$i + 1]['timestamp'] : $endMs;

                // Clip to range
                $s = max($segStart, $startMs);
                $e = min($segEnd, $endMs);
                if ($e <= $s) continue;

                $x = $this->lMargin + (($s - $startMs) / $duration) * $barWidth;
                $w = max(1, (($e - $s) / $duration) * $barWidth);

                // Get color for state
                $state = $cur['state'] ?? 0;
                $stateText = isset($cur['state_text']) ? strtoupper($cur['state_text']) : stateLabel($state, $isHost);
                $col = $colorMap[$stateText] ?? $colorMap[$state] ?? [200, 200, 200];

                // Draw segment with gradient effect
                $this->SetFillColor($col[0], $col[1], $col[2]);
                $this->Rect($x, $y + 1, $w, $barHeight - 2, 'F');

                // Add subtle highlight on top
                $lighterCol = array_map(function($c) { return min(255, $c + 20); }, $col);
                $this->SetFillColor($lighterCol[0], $lighterCol[1], $lighterCol[2]);
                $this->Rect($x, $y + 1, $w, 2, 'F');

                // Add segment borders for definition
                if ($w > 2) {
                    $this->SetDrawColor(0, 0, 0);
                    $this->SetLineWidth(0.2);
                    $this->Line($x, $y + 1, $x, $y + $barHeight - 1);
                    $this->Line($x + $w, $y + 1, $x + $w, $y + $barHeight - 1);
                }
            }

            return $y + $barHeight + 6;
        }
    }
}

// Calculate date range information
$rangeDays = ceil(($endTs - $startTs) / 86400);
$titleRangeTxt = $rangeDays === 1 ? 'Last 24 Hours' : ('Last ' . $rangeDays . ' Days');

// Determine host display name
$ipRegex = '/^(?:\d{1,3}\.){3}\d{1,3}$/';
$hostDisplay = preg_match($ipRegex, $hostname) ? $hostname : ($hostIp ? "{$hostname} ({$hostIp})" : $hostname);

// Create PDF instance
$pdf = new BleskAvailabilityPDF('L', 'mm', 'A4'); // Landscape orientation like JavaScript version
$pdf->AliasNbPages();
$pdf->SetAuthor('Blesk');

// Header content
$pdf->titleText = "{$hostDisplay} Availability Report ({$titleRangeTxt})";
$pdf->rangeText = 'Range: ' . date('Y-m-d H:i', $startTs) . ' - ' . date('Y-m-d H:i', $endTs);
$pdf->statusText = 'Host: ' . $hostDisplay . ' | Generated: ' . date('Y-m-d H:i:s');

// Use the logo from reports functions
$logoPath = dirname(__DIR__) . '/reportsFunctions/blesk-logo-black_ret.png';
if (file_exists($logoPath)) {
    $pdf->logoPath = $logoPath;
}

$pdf->SetTitle($pdf->titleText);
$pdf->AddPage();

// Common helpers
$pdf->SetAutoPageBreak(true, 15);

/**
 * Draw a light grey section title spanning full width with better styling
 */
$drawSectionTitle = function(string $title) use ($pdf) {
    $pdf->SetFont('Arial', 'B', 14);
    $pdf->SetFillColor(240, 240, 240);
    $pdf->SetTextColor(51, 51, 51);
    $pdf->Cell(0, 10, $title, 0, 1, 'L', true);
    $pdf->Ln(3);
};

/**
 * Draw a table header row with uniform style
 */
$drawTableHeader = function(array $headers, array $widths) use ($pdf) {
    $pdf->SetFont('Arial', 'B', 9);
    $pdf->SetFillColor(245, 245, 245);
    $pdf->SetTextColor(51, 51, 51);
    foreach ($headers as $idx => $hdr) {
        $pdf->Cell($widths[$idx], 7, $hdr, 1, 0, 'C', true);
    }
    $pdf->Ln();
};

/**
 * Draw a numeric percentage cell using fill colours similar to JavaScript version
 */
$pctCell = function(float $value, float $width, string $kind) use ($pdf) {
    $fill = false;
    if ($value > 0) {
        switch ($kind) {
            case 'ok':
            case 'up':
                $pdf->SetFillColor(165, 214, 167); // green
                break;
            case 'warn':
            case 'warning':
                $pdf->SetFillColor(255, 224, 130); // yellow
                break;
            case 'crit':
            case 'down':
            case 'critical':
                $pdf->SetFillColor(229, 115, 115); // red
                break;
            default: // unknown / unreachable
                $pdf->SetFillColor(169, 182, 201); // grey
        }
        $fill = true;
        $pdf->SetTextColor(51, 51, 51);
    }
    $pdf->Cell($width, 6, number_format($value, 1), 1, 0, 'R', $fill);
    if ($fill) $pdf->SetTextColor(0); // reset
};

// --------------------------------------------------------------------
// Host Availability Summary
// --------------------------------------------------------------------
$hostAvailData = $hostData['host'] ?? null;
if ($hostAvailData) {
    $drawSectionTitle('Host Availability Summary');

    $up = ($hostAvailData['time_up'] ?? 0) + ($hostAvailData['scheduled_time_up'] ?? 0);
    $down = ($hostAvailData['time_down'] ?? 0) + ($hostAvailData['scheduled_time_down'] ?? 0);
    $unreach = ($hostAvailData['time_unreachable'] ?? 0) + ($hostAvailData['scheduled_time_unreachable'] ?? 0);
    $total = $up + $down + $unreach;

    $upPct = percent($up, $total);
    $downPct = percent($down, $total);
    $unreachPct = percent($unreach, $total);

    // Table headers
    $hdrs = ['Host', 'Up %', 'Down %', 'Unreach %'];
    $pageWidth = $pdf->ContentWidth();
    $pctW = 25; // fixed width for percentage columns
    $widths = [$pageWidth - 3 * $pctW, $pctW, $pctW, $pctW];
    $drawTableHeader($hdrs, $widths);

    // Data row
    $pdf->SetFont('Arial', '', 8);
    $pdf->Cell($widths[0], 6, $hostDisplay, 1);
    $pctCell($upPct, $widths[1], 'up');
    $pctCell($downPct, $widths[2], 'down');
    $pctCell($unreachPct, $widths[3], 'unknown');
    $pdf->Ln();
    $pdf->Ln(5);
}

// --------------------------------------------------------------------
// Host State Timeline
// --------------------------------------------------------------------
if (!empty($hostTimeline)) {
    $drawSectionTitle('Host State Timeline');

    // Color mapping for host states (matching JavaScript version)
    $hostColorMap = [
        'UP' => [165, 214, 167], 'up' => [165, 214, 167], 2 => [165, 214, 167], 0 => [165, 214, 167],
        'DOWN' => [229, 115, 115], 'down' => [229, 115, 115], 4 => [229, 115, 115], 1 => [229, 115, 115],
        'UNREACHABLE' => [169, 182, 201], 'unreachable' => [169, 182, 201], 8 => [169, 182, 201]
    ];

    $curY = $pdf->GetY();
    $curY = $pdf->drawTimelineBar($hostTimeline, $startTs, $endTs, $curY, $hostColorMap, true);
    $pdf->SetY($curY);

    // Table of state changes
    if (!empty($hostTimeline)) {
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(80, 7, 'Timestamp', 1, 0, 'C', true);
        $pdf->Cell(40, 7, 'State', 1, 1, 'C', true);

        $pdf->SetFont('Arial', '', 8);
        foreach ($hostTimeline as $sc) {
            $stateText = isset($sc['state_text']) ? strtoupper($sc['state_text']) : stateLabel($sc['state'], true);
            $pdf->Cell(80, 6, date('Y-m-d H:i:s', $sc['timestamp']), 1);
            $pdf->Cell(40, 6, $stateText, 1, 1);
        }
        $pdf->Ln(5);
    }
}

// --------------------------------------------------------------------
// Service Availability Summary
// --------------------------------------------------------------------
if (!empty($services)) {
    // Add new page for services (like JavaScript version)
    $pdf->AddPage();

    $drawSectionTitle('Service Availability Summary');

    // Table headers
    $hdrsSvc = ['Service', 'OK %', 'Warn %', 'Crit %', 'Unk %'];
    $pageWidth = $pdf->ContentWidth();
    $pctW = 25;
    $wSvc = [$pageWidth - 4 * $pctW, $pctW, $pctW, $pctW, $pctW];
    $drawTableHeader($hdrsSvc, $wSvc);

    $pdf->SetFont('Arial', '', 8);
    foreach ($services as $svc) {
        $total = ($svc['time_ok'] ?? 0) + ($svc['time_warning'] ?? 0) + ($svc['time_critical'] ?? 0) + ($svc['time_unknown'] ?? 0);
        $okPct = percent($svc['time_ok'] ?? 0, $total);
        $warnPct = percent($svc['time_warning'] ?? 0, $total);
        $critPct = percent($svc['time_critical'] ?? 0, $total);
        $unkPct = percent($svc['time_unknown'] ?? 0, $total);

        $pdf->Cell($wSvc[0], 6, $svc['description'] ?? '-', 1);
        $pctCell($okPct, $wSvc[1], 'ok');
        $pctCell($warnPct, $wSvc[2], 'warn');
        $pctCell($critPct, $wSvc[3], 'crit');
        $pctCell($unkPct, $wSvc[4], 'unknown');
        $pdf->Ln();
    }
    $pdf->Ln(5);
}

// --------------------------------------------------------------------
// Service State Change Timelines
// --------------------------------------------------------------------
if (!empty($services)) {
    // Service color mapping (matching JavaScript version)
    $svcColorMap = [
        'OK' => [165, 214, 167], 'ok' => [165, 214, 167], 2 => [165, 214, 167], 0 => [165, 214, 167],
        'WARNING' => [255, 224, 130], 'warning' => [255, 224, 130], 4 => [255, 224, 130], 1 => [255, 224, 130],
        'CRITICAL' => [229, 115, 115], 'critical' => [229, 115, 115], 16 => [229, 115, 115],
        'UNKNOWN' => [169, 182, 201], 'unknown' => [169, 182, 201], 8 => [169, 182, 201], 3 => [169, 182, 201]
    ];

    foreach ($services as $svc) {
        $serviceDescription = $svc['description'] ?? '';
        if (empty($serviceDescription)) continue;

        // Fetch service timeline
        $svcTimeline = fetchTimelineJson($hostname, $startTs, $endTs, 'service', $serviceDescription);
        if (empty($svcTimeline)) continue;

        $pdf->AddPage();
        $pdf->SetFont('Arial', 'B', 11);
        $pdf->Cell(0, 8, "Service Trends - {$serviceDescription}", 0, 1, 'L');
        $pdf->Ln(3);

        // Draw timeline bar
        $curY = $pdf->GetY();
        $curY = $pdf->drawTimelineBar($svcTimeline, $startTs, $endTs, $curY, $svcColorMap, false);
        $pdf->SetY($curY);

        // Table of changes
        $pdf->SetFont('Arial', 'B', 9);
        $pdf->SetFillColor(245, 245, 245);
        $pdf->SetTextColor(51, 51, 51);
        $pdf->Cell(80, 7, 'Timestamp', 1, 0, 'C', true);
        $pdf->Cell(40, 7, 'State', 1, 1, 'C', true);

        $pdf->SetFont('Arial', '', 8);
        foreach ($svcTimeline as $ev) {
            $stateText = isset($ev['state_text']) ? strtoupper($ev['state_text']) : stateLabel($ev['state'], false);
            $pdf->Cell(80, 6, date('Y-m-d H:i:s', $ev['timestamp']), 1);
            $pdf->Cell(40, 6, $stateText, 1, 1);
        }
    }
}

// Generate filename and output PDF
$timestamp = date('Ymd_His');
$filename = "availability_report_{$hostname}_{$timestamp}.pdf";

// Set headers for PDF download
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: private, max-age=0, must-revalidate');
header('Pragma: public');

// Output PDF directly
$pdf->Output('I', $filename);

?>
