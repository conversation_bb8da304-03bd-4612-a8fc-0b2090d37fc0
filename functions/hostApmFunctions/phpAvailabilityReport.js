(function(){
    // Inject Export PDF button once DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        const controls = document.querySelector('#availability-container .availability-controls');
        if (controls && !document.getElementById('availability-export')) {
            const btn = document.createElement('button');
            btn.id = 'availability-export';
            btn.className = 'availability-export';
            btn.title = 'Export availability & trends as PDF';
            btn.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            controls.appendChild(btn);

            btn.addEventListener('click', () => {
                exportPdf(btn);
            });
        }
    });

    // Keep track of current host name (populated from hostLoaded event)
    let currentHost = null;
    document.addEventListener('hostLoaded', e => {
        currentHost = e.detail.hostname;
    });

    // ---------------- PHP PDF generation -----------------
    async function exportPdf(buttonEl){
        try {
            if (!currentHost){
                alert('Please load a host first');
                return;
            }

            const startInput = document.getElementById('availability-start');
            const endInput   = document.getElementById('availability-end');
            const startTs = Math.floor(new Date(startInput.value).getTime()/1000);
            const endTs   = Math.floor(new Date(endInput.value).getTime()/1000);
            if (isNaN(startTs) || isNaN(endTs) || endTs <= startTs){
                alert('Invalid date range');
                return;
            }

            // Disable button + spinner
            const originalHtml = buttonEl.innerHTML;
            buttonEl.disabled = true;
            buttonEl.innerHTML = '<i class="fa fa-spinner fa-spin"></i>';

            // Get host IP from URL parameters
            const hostIp = (new URLSearchParams(window.location.search)).get('hostip') || 
                          (new URLSearchParams(window.location.search)).get('ip') || '';

            // Prepare form data for PHP request
            const formData = new FormData();
            formData.append('hostname', currentHost);
            formData.append('hostIp', hostIp);
            formData.append('startTs', startTs);
            formData.append('endTs', endTs);

            // Send request to PHP PDF generator
            const response = await fetch('functions/hostApmFunctions/generateAvailabilityReport.php', {
                method: 'POST',
                body: formData,
                credentials: 'include'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // Check if response is JSON (error) or PDF (success)
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                // Error response
                const result = await response.json();
                throw new Error(result.message || 'Unknown error occurred');
            } else {
                // PDF response - trigger download
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                
                // Extract filename from Content-Disposition header or use default
                const disposition = response.headers.get('Content-Disposition');
                let filename = `availability_report_${currentHost}_${new Date().toISOString().replace(/[:T]/g,'-').split('.')[0]}.pdf`;
                if (disposition && disposition.includes('filename=')) {
                    const matches = disposition.match(/filename="?([^"]+)"?/);
                    if (matches && matches[1]) {
                        filename = matches[1];
                    }
                }
                
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            }

        } catch (err){
            console.error('PDF export error', err);
            alert('Error generating PDF: ' + err.message);
        } finally {
            if (buttonEl){
                buttonEl.disabled = false;
                buttonEl.innerHTML = '<i class="fa fa-file-pdf-o" aria-hidden="true"></i>';
            }
        }
    }
})();
